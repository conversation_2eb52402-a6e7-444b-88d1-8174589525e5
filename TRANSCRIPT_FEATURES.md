# Transcript Import and Subtitle Generation Features

This video editor now supports importing full transcripts and automatically converting them into subtitles with proper timing and formatting.

## Features

### 🎯 **Multi-Format Support**
- **Plain Text**: Simple text files with automatic paragraph/sentence detection
- **SRT**: Standard SubRip subtitle format with timing
- **VTT/WebVTT**: Web Video Text Tracks format
- **JSON**: Custom JSON format for programmatic integration

### 🔄 **Automatic Conversion**
- **Smart Text Splitting**: Automatically splits long text into appropriately sized subtitle segments
- **Timing Distribution**: Evenly distributes timing across segments when no timing is provided
- **Character Limits**: Respects maximum character limits per subtitle for readability
- **Duration Calculation**: Uses video duration or custom settings for timing

### 🎨 **Customizable Styling**
- Font size, color, and family
- Background color and transparency
- Position (top, center, bottom)
- All standard subtitle styling options

### 📝 **Preview and Edit**
- Real-time preview of generated subtitles
- Edit timing and text before importing
- Bulk import with progress tracking
- Export subtitles in SRT format

## How to Use

### 1. Access Transcript Import
1. Open the video editor
2. In the right panel, find the "Subtitles" section
3. Click the "Import" button to open the transcript importer

### 2. Import Your Transcript
**Option A: Upload File**
- Click "Upload File" and select your transcript file
- Supported formats: `.txt`, `.srt`, `.vtt`, `.webvtt`, `.json`
- Format will be auto-detected based on file extension

**Option B: Paste Text**
- Paste your transcript content directly into the text area
- Select the format manually or use "Auto" for automatic detection

### 3. Configure Settings (Optional)
Click "Advanced Settings" to customize:
- **Default Duration**: How long each subtitle should appear (1-10 seconds)
- **Max Characters**: Maximum characters per subtitle (40-120)
- **Font Size**: Subtitle font size (12-48px)
- **Position**: Where subtitles appear (top/center/bottom)
- **Colors**: Text and background colors
- **Font Family**: Choose from available fonts

### 4. Preview and Import
1. Click "Generate Subtitles" to process your transcript
2. Review the preview showing generated subtitles with timing
3. Click "Import X Subtitles" to add them to your project

## Supported Transcript Formats

### Plain Text
```
This is the first paragraph of the transcript.

This is the second paragraph with more content.
It can span multiple lines.

Final paragraph here.
```

### SRT Format
```
1
00:00:01,000 --> 00:00:04,000
Hello, this is the first subtitle.

2
00:00:05,000 --> 00:00:08,000
This is the second subtitle.
```

### VTT Format
```
WEBVTT

00:00:01.000 --> 00:00:04.000
Hello, this is the first subtitle.

00:00:05.000 --> 00:00:08.000
This is the second subtitle.
```

### JSON Format
```json
{
  "segments": [
    {
      "text": "First segment text",
      "startTime": 0,
      "endTime": 3
    },
    {
      "text": "Second segment text", 
      "startTime": 3,
      "endTime": 6
    }
  ],
  "totalDuration": 6
}
```

## Advanced Features

### Bulk Subtitle Management
- **Import Multiple**: Add transcript-generated subtitles to existing ones
- **Clear All**: Remove all subtitles at once
- **Export SRT**: Download all subtitles as an SRT file
- **Subtitle Count**: See total number of subtitles and duration

### Smart Text Processing
- **Long Text Splitting**: Automatically breaks long paragraphs into readable chunks
- **Word Boundary Respect**: Splits at word boundaries, not mid-word
- **Timing Optimization**: Distributes timing based on text length and video duration
- **Format Detection**: Automatically detects transcript format from content

### Integration with Video Editor
- **Timeline Integration**: Subtitles appear on the timeline for precise editing
- **Real-time Preview**: See subtitles overlaid on video during playback
- **Export Support**: Subtitles are included in video exports
- **Undo/Redo**: Full undo/redo support for subtitle operations

## Tips for Best Results

1. **Clean Your Transcript**: Remove speaker names, timestamps, and formatting before import
2. **Check Video Duration**: Ensure your video is loaded first for accurate timing
3. **Adjust Character Limits**: Shorter limits work better for mobile viewing
4. **Preview Before Import**: Always review the generated subtitles before importing
5. **Use Appropriate Duration**: 2-4 seconds per subtitle is usually optimal
6. **Consider Reading Speed**: Average reading speed is about 200 words per minute

## Troubleshooting

**Issue**: Subtitles are too fast/slow
- **Solution**: Adjust the "Default Duration" setting or check your video duration

**Issue**: Text is cut off or too long
- **Solution**: Reduce "Max Characters" setting or manually edit long segments

**Issue**: Format not detected correctly
- **Solution**: Manually select the format instead of using "Auto"

**Issue**: Import button is disabled
- **Solution**: Make sure you have transcript content and the video is loaded

## Technical Details

The transcript import system uses:
- **TranscriptParser**: Core parsing engine for multiple formats
- **TranscriptImporter**: React component for user interface
- **Automatic Timing**: Algorithm for distributing timing across segments
- **Text Chunking**: Smart text splitting respecting word boundaries
- **Format Detection**: Regex-based format identification

All imported subtitles integrate seamlessly with the existing subtitle system, supporting all editing, styling, and export features.
