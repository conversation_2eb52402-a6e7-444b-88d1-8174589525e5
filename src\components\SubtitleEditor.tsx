import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Type, Trash2, Plus, FileText, Download, Upload } from 'lucide-react';
import { TranscriptImporter } from './TranscriptImporter';

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

interface SubtitleEditorProps {
  subtitles: Subtitle[];
  selectedSubtitles: string[];
  currentTime: number;
  videoDuration?: number;
  onAddSubtitle: () => void;
  onUpdateSubtitle: (id: string, updates: Partial<Subtitle>) => void;
  onDeleteSubtitle: (id: string) => void;
  onSelectSubtitle: (id: string) => void;
  onImportSubtitles?: (subtitles: Subtitle[]) => void;
  onDeleteAllSubtitles?: () => void;
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 100);
  return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
};

const parseTime = (timeStr: string): number => {
  const parts = timeStr.split(':');
  if (parts.length !== 2) return 0;
  
  const [mins, secsMs] = parts;
  const [secs, ms = '0'] = secsMs.split('.');
  
  return parseInt(mins) * 60 + parseInt(secs) + parseInt(ms) / 100;
};

export const SubtitleEditor: React.FC<SubtitleEditorProps> = ({
  subtitles,
  selectedSubtitles,
  currentTime,
  videoDuration,
  onAddSubtitle,
  onUpdateSubtitle,
  onDeleteSubtitle,
  onSelectSubtitle,
  onImportSubtitles,
  onDeleteAllSubtitles
}) => {
  const [editingText, setEditingText] = useState<string | null>(null);
  const [showTranscriptImporter, setShowTranscriptImporter] = useState(false);
  
  const selectedSubtitle = selectedSubtitles.length > 0 
    ? subtitles.find(s => s.id === selectedSubtitles[0])
    : null;

  const handleTextEdit = (subtitle: Subtitle, newText: string) => {
    onUpdateSubtitle(subtitle.id, { text: newText });
    setEditingText(null);
  };

  const handleTimeChange = (subtitle: Subtitle, field: 'startTime' | 'endTime', value: string) => {
    const time = parseTime(value);
    if (field === 'startTime' && time < subtitle.endTime) {
      onUpdateSubtitle(subtitle.id, { startTime: time });
    } else if (field === 'endTime' && time > subtitle.startTime) {
      onUpdateSubtitle(subtitle.id, { endTime: time });
    }
  };

  const handleImportSubtitles = (importedSubtitles: Subtitle[]) => {
    if (onImportSubtitles) {
      onImportSubtitles(importedSubtitles);
    }
    setShowTranscriptImporter(false);
  };

  const handleExportSubtitles = () => {
    if (subtitles.length === 0) return;

    // Export as SRT format
    const srtContent = subtitles
      .map((subtitle, index) => {
        const startTime = formatSRTTime(subtitle.startTime);
        const endTime = formatSRTTime(subtitle.endTime);
        return `${index + 1}\n${startTime} --> ${endTime}\n${subtitle.text}\n`;
      })
      .join('\n');

    const blob = new Blob([srtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subtitles.srt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatSRTTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
  };

  if (showTranscriptImporter) {
    return (
      <TranscriptImporter
        onImportSubtitles={handleImportSubtitles}
        videoDuration={videoDuration}
        onClose={() => setShowTranscriptImporter(false)}
      />
    );
  }

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Type className="w-4 h-4" />
          <h3 className="font-semibold">Subtitles</h3>
          {subtitles.length > 0 && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {subtitles.length}
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setShowTranscriptImporter(true)}
            size="sm"
            variant="outline"
            className="flex items-center gap-1"
          >
            <FileText className="w-3 h-3" />
            Import
          </Button>
          {subtitles.length > 0 && (
            <Button
              onClick={handleExportSubtitles}
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
            >
              <Download className="w-3 h-3" />
              Export
            </Button>
          )}
          <Button onClick={onAddSubtitle} size="sm" className="flex items-center gap-1">
            <Plus className="w-3 h-3" />
            Add
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {subtitles.length > 0 && (
        <div className="flex items-center justify-between text-xs text-gray-500 border-b pb-2">
          <span>
            Total duration: {Math.max(...subtitles.map(s => s.endTime)).toFixed(1)}s
          </span>
          {onDeleteAllSubtitles && (
            <Button
              onClick={onDeleteAllSubtitles}
              size="sm"
              variant="ghost"
              className="text-red-500 hover:text-red-700 h-6 px-2"
            >
              Clear All
            </Button>
          )}
        </div>
      )}

      {/* Subtitle List */}
      <div className="space-y-2 max-h-40 overflow-y-auto">
        {subtitles.map((subtitle) => {
          const isSelected = selectedSubtitles.includes(subtitle.id);
          const isActive = currentTime >= subtitle.startTime && currentTime < subtitle.endTime;
          
          return (
            <div
              key={subtitle.id}
              className={`p-2 border rounded cursor-pointer transition-colors ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              } ${isActive ? 'ring-2 ring-green-400' : ''}`}
              onClick={() => onSelectSubtitle(subtitle.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  {editingText === subtitle.id ? (
                    <Input
                      value={subtitle.text}
                      onChange={(e) => onUpdateSubtitle(subtitle.id, { text: e.target.value })}
                      onBlur={() => setEditingText(null)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setEditingText(null);
                        }
                      }}
                      autoFocus
                      className="text-sm"
                    />
                  ) : (
                    <div
                      className="text-sm font-medium truncate"
                      onDoubleClick={() => setEditingText(subtitle.id)}
                    >
                      {subtitle.text}
                    </div>
                  )}
                  <div className="text-xs text-gray-500 mt-1">
                    {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteSubtitle(subtitle.id);
                  }}
                  className="ml-2 p-1 h-6 w-6"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Subtitle Properties */}
      {selectedSubtitle && (
        <div className="space-y-4 border-t pt-4">
          <h4 className="font-medium text-sm">Subtitle Properties</h4>
          
          {/* Text */}
          <div className="space-y-2">
            <Label className="text-xs">Text</Label>
            <Textarea
              value={selectedSubtitle.text}
              onChange={(e) => onUpdateSubtitle(selectedSubtitle.id, { text: e.target.value })}
              className="text-sm"
              rows={2}
            />
          </div>

          {/* Timing */}
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label className="text-xs">Start Time</Label>
              <Input
                value={formatTime(selectedSubtitle.startTime)}
                onChange={(e) => handleTimeChange(selectedSubtitle, 'startTime', e.target.value)}
                className="text-xs"
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">End Time</Label>
              <Input
                value={formatTime(selectedSubtitle.endTime)}
                onChange={(e) => handleTimeChange(selectedSubtitle, 'endTime', e.target.value)}
                className="text-xs"
              />
            </div>
          </div>

          {/* Font Size */}
          <div className="space-y-2">
            <Label className="text-xs">Font Size: {selectedSubtitle.fontSize}px</Label>
            <Slider
              value={[selectedSubtitle.fontSize]}
              onValueChange={([value]) => onUpdateSubtitle(selectedSubtitle.id, { fontSize: value })}
              min={12}
              max={72}
              step={2}
              className="w-full"
            />
          </div>

          {/* Position */}
          <div className="space-y-2">
            <Label className="text-xs">Position</Label>
            <Select
              value={selectedSubtitle.position}
              onValueChange={(value: 'top' | 'center' | 'bottom') => 
                onUpdateSubtitle(selectedSubtitle.id, { position: value })
              }
            >
              <SelectTrigger className="text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Colors */}
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label className="text-xs">Text Color</Label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={selectedSubtitle.fontColor}
                  onChange={(e) => onUpdateSubtitle(selectedSubtitle.id, { fontColor: e.target.value })}
                  className="w-8 h-8 rounded border"
                />
                <Input
                  value={selectedSubtitle.fontColor}
                  onChange={(e) => onUpdateSubtitle(selectedSubtitle.id, { fontColor: e.target.value })}
                  className="text-xs flex-1"
                />
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Background</Label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={selectedSubtitle.backgroundColor.replace(/rgba?\([^)]+\)/, '#000000')}
                  onChange={(e) => onUpdateSubtitle(selectedSubtitle.id, { backgroundColor: e.target.value })}
                  className="w-8 h-8 rounded border"
                />
                <Input
                  value={selectedSubtitle.backgroundColor}
                  onChange={(e) => onUpdateSubtitle(selectedSubtitle.id, { backgroundColor: e.target.value })}
                  className="text-xs flex-1"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};