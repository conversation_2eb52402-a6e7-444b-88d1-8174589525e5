import { TranscriptParser } from '../transcriptParser';

describe('TranscriptParser', () => {
  describe('parseTranscript', () => {
    it('should parse plain text transcript', () => {
      const content = `Hello, this is the first sentence.
      
This is the second paragraph with more content.

And this is the third paragraph.`;

      const result = TranscriptParser.parseTranscript(content);
      
      expect(result.format).toBe('plain');
      expect(result.segments).toHaveLength(3);
      expect(result.segments[0].text).toBe('Hello, this is the first sentence.');
      expect(result.segments[1].text).toBe('This is the second paragraph with more content.');
      expect(result.segments[2].text).toBe('And this is the third paragraph.');
    });

    it('should parse SRT format transcript', () => {
      const content = `1
00:00:01,000 --> 00:00:04,000
Hello, this is the first subtitle.

2
00:00:05,000 --> 00:00:08,000
This is the second subtitle.`;

      const result = TranscriptParser.parseTranscript(content);
      
      expect(result.format).toBe('srt');
      expect(result.segments).toHaveLength(2);
      expect(result.segments[0].text).toBe('Hello, this is the first subtitle.');
      expect(result.segments[0].startTime).toBe(1);
      expect(result.segments[0].endTime).toBe(4);
      expect(result.segments[1].text).toBe('This is the second subtitle.');
      expect(result.segments[1].startTime).toBe(5);
      expect(result.segments[1].endTime).toBe(8);
    });

    it('should parse VTT format transcript', () => {
      const content = `WEBVTT

00:00:01.000 --> 00:00:04.000
Hello, this is the first subtitle.

00:00:05.000 --> 00:00:08.000
This is the second subtitle.`;

      const result = TranscriptParser.parseTranscript(content);
      
      expect(result.format).toBe('vtt');
      expect(result.segments).toHaveLength(2);
      expect(result.segments[0].text).toBe('Hello, this is the first subtitle.');
      expect(result.segments[0].startTime).toBe(1);
      expect(result.segments[0].endTime).toBe(4);
    });

    it('should parse JSON format transcript', () => {
      const content = JSON.stringify({
        segments: [
          { text: 'First segment', startTime: 0, endTime: 3 },
          { text: 'Second segment', startTime: 3, endTime: 6 }
        ]
      });

      const result = TranscriptParser.parseTranscript(content);
      
      expect(result.format).toBe('json');
      expect(result.segments).toHaveLength(2);
      expect(result.segments[0].text).toBe('First segment');
      expect(result.segments[0].startTime).toBe(0);
      expect(result.segments[0].endTime).toBe(3);
    });
  });

  describe('convertToSubtitles', () => {
    it('should convert plain text segments to subtitles with timing', () => {
      const parsedTranscript = {
        segments: [
          { text: 'First segment' },
          { text: 'Second segment' },
          { text: 'Third segment' }
        ],
        format: 'plain' as const
      };

      const subtitles = TranscriptParser.convertToSubtitles(parsedTranscript, {
        videoDuration: 9,
        defaultDuration: 3
      });

      expect(subtitles).toHaveLength(3);
      expect(subtitles[0].text).toBe('First segment');
      expect(subtitles[0].startTime).toBe(0);
      expect(subtitles[0].endTime).toBe(3);
      expect(subtitles[1].startTime).toBe(3);
      expect(subtitles[1].endTime).toBe(6);
      expect(subtitles[2].startTime).toBe(6);
      expect(subtitles[2].endTime).toBe(9);
    });

    it('should preserve existing timing from segments', () => {
      const parsedTranscript = {
        segments: [
          { text: 'First segment', startTime: 1, endTime: 4 },
          { text: 'Second segment', startTime: 5, endTime: 8 }
        ],
        format: 'srt' as const
      };

      const subtitles = TranscriptParser.convertToSubtitles(parsedTranscript);

      expect(subtitles).toHaveLength(2);
      expect(subtitles[0].startTime).toBe(1);
      expect(subtitles[0].endTime).toBe(4);
      expect(subtitles[1].startTime).toBe(5);
      expect(subtitles[1].endTime).toBe(8);
    });

    it('should split long text into smaller chunks', () => {
      const longText = 'This is a very long text that should be split into multiple subtitles because it exceeds the maximum character limit that we have set for individual subtitle segments.';
      
      const parsedTranscript = {
        segments: [{ text: longText }],
        format: 'plain' as const
      };

      const subtitles = TranscriptParser.convertToSubtitles(parsedTranscript, {
        maxCharsPerSubtitle: 50,
        videoDuration: 6
      });

      expect(subtitles.length).toBeGreaterThan(1);
      subtitles.forEach(subtitle => {
        expect(subtitle.text.length).toBeLessThanOrEqual(50);
      });
    });

    it('should apply custom styling options', () => {
      const parsedTranscript = {
        segments: [{ text: 'Test segment' }],
        format: 'plain' as const
      };

      const subtitles = TranscriptParser.convertToSubtitles(parsedTranscript, {
        fontSize: 32,
        fontColor: '#FF0000',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        position: 'top',
        fontFamily: 'Helvetica, sans-serif'
      });

      expect(subtitles[0].fontSize).toBe(32);
      expect(subtitles[0].fontColor).toBe('#FF0000');
      expect(subtitles[0].backgroundColor).toBe('rgba(255, 255, 255, 0.8)');
      expect(subtitles[0].position).toBe('top');
      expect(subtitles[0].fontFamily).toBe('Helvetica, sans-serif');
    });
  });

  describe('format detection', () => {
    it('should auto-detect SRT format', () => {
      const srtContent = `1\n00:00:01,000 --> 00:00:04,000\nTest subtitle`;
      const result = TranscriptParser.parseTranscript(srtContent);
      expect(result.format).toBe('srt');
    });

    it('should auto-detect VTT format', () => {
      const vttContent = `WEBVTT\n\n00:00:01.000 --> 00:00:04.000\nTest subtitle`;
      const result = TranscriptParser.parseTranscript(vttContent);
      expect(result.format).toBe('vtt');
    });

    it('should auto-detect JSON format', () => {
      const jsonContent = JSON.stringify({ segments: [{ text: 'Test' }] });
      const result = TranscriptParser.parseTranscript(jsonContent);
      expect(result.format).toBe('json');
    });

    it('should default to plain text format', () => {
      const plainContent = 'Just some plain text content';
      const result = TranscriptParser.parseTranscript(plainContent);
      expect(result.format).toBe('plain');
    });
  });
});
