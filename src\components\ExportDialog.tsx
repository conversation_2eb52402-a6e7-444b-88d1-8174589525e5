
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Download, FileVideo, Settings } from 'lucide-react';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (options: ExportOptions) => void;
  isExporting: boolean;
  exportProgress: number;
}

export interface ExportOptions {
  format: 'mp4' | 'webm' | 'mov';
  quality: 'low' | 'medium' | 'high' | 'ultra';
  resolution: '720p' | '1080p' | '1440p' | '4k';
  framerate: 24 | 30 | 60;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onOpenChange,
  onExport,
  isExporting,
  exportProgress,
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'mp4',
    quality: 'high',
    resolution: '1080p',
    framerate: 30,
  });

  const handleExport = () => {
    onExport(exportOptions);
  };

  const getQualityDescription = (quality: string) => {
    switch (quality) {
      case 'low': return 'Fast export, smaller file size';
      case 'medium': return 'Balanced quality and file size';
      case 'high': return 'High quality, larger file size';
      case 'ultra': return 'Maximum quality, largest file size';
      default: return '';
    }
  };

  const getResolutionDescription = (resolution: string) => {
    switch (resolution) {
      case '720p': return '1280x720 - Good for web sharing';
      case '1080p': return '1920x1080 - Full HD quality';
      case '1440p': return '2560x1440 - 2K quality';
      case '4k': return '3840x2160 - Ultra HD quality';
      default: return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-gray-800 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-blue-400">
            <FileVideo className="w-5 h-5" />
            Export Video
          </DialogTitle>
        </DialogHeader>

        {isExporting ? (
          <div className="space-y-4 py-6">
            <div className="text-center">
              <div className="text-lg font-medium mb-2">Exporting your video...</div>
              <div className="text-sm text-gray-400 mb-4">
                Please wait while we process your video
              </div>
            </div>
            
            <Progress value={exportProgress} className="w-full h-3" />
            
            <div className="text-center text-sm text-gray-400">
              {exportProgress}% complete
            </div>
          </div>
        ) : (
          <div className="space-y-6 py-4">
            {/* Format Selection */}
            <div className="space-y-2">
              <Label className="text-gray-200">Format</Label>
              <Select
                value={exportOptions.format}
                onValueChange={(value: 'mp4' | 'webm' | 'mov') => 
                  setExportOptions(prev => ({ ...prev, format: value }))
                }
              >
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="mp4">MP4 - Universal compatibility</SelectItem>
                  <SelectItem value="webm">WebM - Web optimized</SelectItem>
                  <SelectItem value="mov">MOV - High quality</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quality Selection */}
            <div className="space-y-2">
              <Label className="text-gray-200">Quality</Label>
              <Select
                value={exportOptions.quality}
                onValueChange={(value: 'low' | 'medium' | 'high' | 'ultra') => 
                  setExportOptions(prev => ({ ...prev, quality: value }))
                }
              >
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="ultra">Ultra</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-400">{getQualityDescription(exportOptions.quality)}</p>
            </div>

            {/* Resolution Selection */}
            <div className="space-y-2">
              <Label className="text-gray-200">Resolution</Label>
              <Select
                value={exportOptions.resolution}
                onValueChange={(value: '720p' | '1080p' | '1440p' | '4k') => 
                  setExportOptions(prev => ({ ...prev, resolution: value }))
                }
              >
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="720p">720p HD</SelectItem>
                  <SelectItem value="1080p">1080p Full HD</SelectItem>
                  <SelectItem value="1440p">1440p 2K</SelectItem>
                  <SelectItem value="4k">4K Ultra HD</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-400">{getResolutionDescription(exportOptions.resolution)}</p>
            </div>

            {/* Frame Rate Selection */}
            <div className="space-y-2">
              <Label className="text-gray-200">Frame Rate</Label>
              <Select
                value={exportOptions.framerate.toString()}
                onValueChange={(value: string) => 
                  setExportOptions(prev => ({ ...prev, framerate: parseInt(value) as 24 | 30 | 60 }))
                }
              >
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="24">24 fps - Cinematic</SelectItem>
                  <SelectItem value="30">30 fps - Standard</SelectItem>
                  <SelectItem value="60">60 fps - Smooth</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Export Preview */}
            <Card className="p-4 bg-gray-700 border-gray-600">
              <div className="flex items-center gap-2 mb-2">
                <Settings className="w-4 h-4 text-blue-400" />
                <span className="font-medium text-gray-200">Export Summary</span>
              </div>
              <div className="text-sm text-gray-400 space-y-1">
                <div>Format: {exportOptions.format.toUpperCase()}</div>
                <div>Quality: {exportOptions.quality}</div>
                <div>Resolution: {exportOptions.resolution}</div>
                <div>Frame Rate: {exportOptions.framerate} fps</div>
              </div>
            </Card>
          </div>
        )}

        <DialogFooter>
          {!isExporting && (
            <>
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
              >
                Cancel
              </Button>
              <Button
                onClick={handleExport}
                className="bg-blue-600 hover:bg-blue-500"
              >
                <Download className="w-4 h-4 mr-2" />
                Export Video
              </Button>
            </>
          )}
          {isExporting && (
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
            >
              Hide Progress
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
