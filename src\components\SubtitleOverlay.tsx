import React from 'react';

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

interface SubtitleOverlayProps {
  subtitle: Subtitle | null;
  videoWidth: number;
  videoHeight: number;
}

export const SubtitleOverlay: React.FC<SubtitleOverlayProps> = ({
  subtitle,
  videoWidth,
  videoHeight
}) => {
  if (!subtitle) return null;

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      left: '50%',
      transform: 'translateX(-50%)',
      maxWidth: '90%',
      textAlign: 'center' as const,
      padding: '8px 16px',
      borderRadius: '4px',
      fontSize: `${subtitle.fontSize}px`,
      fontFamily: subtitle.fontFamily,
      color: subtitle.fontColor,
      backgroundColor: subtitle.backgroundColor,
      whiteSpace: 'pre-wrap' as const,
      wordWrap: 'break-word' as const,
      lineHeight: '1.2',
      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
      zIndex: 10,
      pointerEvents: 'none' as const
    };

    switch (subtitle.position) {
      case 'top':
        return {
          ...baseStyles,
          top: '10%'
        };
      case 'center':
        return {
          ...baseStyles,
          top: '50%',
          transform: 'translate(-50%, -50%)'
        };
      case 'bottom':
      default:
        return {
          ...baseStyles,
          bottom: '10%'
        };
    }
  };

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: videoWidth,
        height: videoHeight,
        pointerEvents: 'none',
        zIndex: 5
      }}
    >
      <div style={getPositionStyles()}>
        {subtitle.text}
      </div>
    </div>
  );
};