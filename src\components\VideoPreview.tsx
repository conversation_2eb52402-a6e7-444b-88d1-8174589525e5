import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Upload } from 'lucide-react';
import { VideoControls } from './VideoControls';
import { VideoOverlay } from './VideoOverlay';
import { SubtitleOverlay } from './SubtitleOverlay';

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  color: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}
interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
}
interface VideoPreviewProps {
  currentClip: VideoClip | undefined;
  videoZoom: number;
  onVideoZoomChange: (zoom: number) => void;
  onTimeUpdate: () => void;
  onEnded: () => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  onUploadClick: () => void;
  videoRef: React.RefObject<HTMLVideoElement>;
  // Enhanced props for video controls
  isPlaying?: boolean;
  currentTime?: number;
  volume?: number[];
  playbackRate?: number;
  canGoBack?: boolean;
  canGoForward?: boolean;
  onPlay?: () => void;
  onPause?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number[]) => void;
  onPlaybackRateChange?: (rate: number) => void;
  onSkipBack?: () => void;
  onSkipForward?: () => void;
  onFrameBack?: () => void;
  onFrameForward?: () => void;
  // Subtitle props
  subtitles?: Subtitle[];
  getCurrentSubtitle?: () => Subtitle | undefined;
}
export const VideoPreview: React.FC<VideoPreviewProps> = ({
  currentClip,
  videoZoom,
  onVideoZoomChange,
  onTimeUpdate,
  onEnded,
  onDragOver,
  onDrop,
  onUploadClick,
  videoRef,
  isPlaying = false,
  currentTime = 0,
  volume = [100],
  playbackRate = 1,
  canGoBack = false,
  canGoForward = false,
  onPlay = () => {},
  onPause = () => {},
  onSeek = () => {},
  onVolumeChange = () => {},
  onPlaybackRateChange = () => {},
  onSkipBack = () => {},
  onSkipForward = () => {},
  onFrameBack = () => {},
  onFrameForward = () => {},
  subtitles = [],
  getCurrentSubtitle = () => undefined
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({
    x: 0,
    y: 0
  });
  const [panOffset, setPanOffset] = useState({
    x: 0,
    y: 0
  });
  const [lastPanOffset, setLastPanOffset] = useState({
    x: 0,
    y: 0
  });
  const [smoothZoom, setSmoothZoom] = useState(100);
  const [persistentPanOffset, setPersistentPanOffset] = useState({
    x: 0,
    y: 0
  });
  const [videoQuality, setVideoQuality] = useState<'auto' | 'hd' | 'sd'>('auto');
  const [showOverlays, setShowOverlays] = useState(false);

  // Use ref to track previous zoom to prevent unnecessary updates
  const prevZoomRef = useRef(videoZoom);

  // Smooth zoom transition - only update when videoZoom actually changes
  useEffect(() => {
    if (prevZoomRef.current === videoZoom) return;
    prevZoomRef.current = videoZoom;
    const animateZoom = () => {
      setSmoothZoom(prevZoom => {
        const diff = videoZoom - prevZoom;
        if (Math.abs(diff) < 1) {
          return videoZoom;
        }
        return prevZoom + diff * 0.1;
      });
    };
    const interval = setInterval(animateZoom, 16);
    return () => clearInterval(interval);
  }, [videoZoom]);

  // Reset pan when zoom goes to 100% or below - use smoothZoom state directly
  useEffect(() => {
    if (smoothZoom <= 100) {
      setPanOffset({
        x: 0,
        y: 0
      });
      setLastPanOffset({
        x: 0,
        y: 0
      });
      setPersistentPanOffset({
        x: 0,
        y: 0
      });
    }
  }, [smoothZoom]);
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (smoothZoom > 100 && currentClip) {
      setIsPanning(true);
      setPanStart({
        x: e.clientX,
        y: e.clientY
      });
      e.preventDefault();
    }
  }, [smoothZoom, currentClip]);
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning && smoothZoom > 100) {
      const deltaX = e.clientX - panStart.x;
      const deltaY = e.clientY - panStart.y;
      const newPanOffset = {
        x: lastPanOffset.x + deltaX,
        y: lastPanOffset.y + deltaY
      };
      setPanOffset(newPanOffset);
      setPersistentPanOffset(newPanOffset);
    }
  }, [isPanning, panStart, lastPanOffset, smoothZoom]);
  const handleMouseUp = useCallback(() => {
    if (isPanning) {
      setIsPanning(false);
      setLastPanOffset(panOffset);
    }
  }, [isPanning, panOffset]);

  // Add mouse event listeners to document for smoother panning
  useEffect(() => {
    const handleDocumentMouseMove = (e: MouseEvent) => {
      if (isPanning && smoothZoom > 100) {
        const deltaX = e.clientX - panStart.x;
        const deltaY = e.clientY - panStart.y;
        const newPanOffset = {
          x: lastPanOffset.x + deltaX,
          y: lastPanOffset.y + deltaY
        };
        setPanOffset(newPanOffset);
        setPersistentPanOffset(newPanOffset);
      }
    };
    const handleDocumentMouseUp = () => {
      if (isPanning) {
        setIsPanning(false);
        setLastPanOffset(panOffset);
      }
    };
    if (isPanning) {
      document.addEventListener('mousemove', handleDocumentMouseMove);
      document.addEventListener('mouseup', handleDocumentMouseUp);
    }
    return () => {
      document.removeEventListener('mousemove', handleDocumentMouseMove);
      document.removeEventListener('mouseup', handleDocumentMouseUp);
    };
  }, [isPanning, panStart, lastPanOffset, panOffset, smoothZoom]);

  // Enhanced fullscreen functionality
  const handleFullscreen = useCallback(() => {
    if (!containerRef.current) return;
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      containerRef.current.requestFullscreen();
    }
  }, []);

  // Frame-accurate navigation
  const handleFrameNavigation = useCallback((direction: 'forward' | 'back') => {
    if (!videoRef.current || !currentClip) return;
    const frameRate = 30; // Assuming 30fps
    const frameTime = 1 / frameRate;
    const newTime = direction === 'forward' ? currentTime + frameTime : currentTime - frameTime;
    onSeek(Math.max(0, Math.min(currentClip.duration, newTime)));
  }, [currentTime, currentClip, onSeek]);

  // Enhanced playback rate handling
  const handlePlaybackRateChange = useCallback((rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
    }
    onPlaybackRateChange(rate);
  }, [onPlaybackRateChange]);

  // Keyboard shortcuts for video controls
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!currentClip) return;
      switch (e.key) {
        case ' ':
          e.preventDefault();
          isPlaying ? onPause() : onPlay();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          if (e.shiftKey) {
            onSkipBack();
          } else {
            handleFrameNavigation('back');
          }
          break;
        case 'ArrowRight':
          e.preventDefault();
          if (e.shiftKey) {
            onSkipForward();
          } else {
            handleFrameNavigation('forward');
          }
          break;
        case 'f':
          e.preventDefault();
          handleFullscreen();
          break;
        case 'g':
          e.preventDefault();
          setShowOverlays(!showOverlays);
          break;
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [currentClip, isPlaying, onPlay, onPause, onSkipBack, onSkipForward, handleFrameNavigation, handleFullscreen, showOverlays]);

  // Use current pan offset based on zoom level
  const currentPanOffset = smoothZoom > 100 ? persistentPanOffset : {
    x: 0,
    y: 0
  };

  // Enhanced video style with size constraints
  const videoStyle = {
    transform: `scale(${smoothZoom / 100}) translate(${currentPanOffset.x / (smoothZoom / 100)}px, ${currentPanOffset.y / (smoothZoom / 100)}px)`,
    cursor: smoothZoom > 100 ? isPanning ? 'grabbing' : 'grab' : 'default',
    transformOrigin: 'center center',
    transition: 'none',
    maxWidth: '100%',
    maxHeight: '100%',
    width: 'auto',
    height: 'auto',
    objectFit: 'contain' as const
  };

  return <div className="flex-1 flex flex-col bg-black relative pt-6 py-[22px] min-h-0">
      {/* Video Container */}
      <div ref={containerRef} className="flex-1 relative flex items-center justify-center overflow-hidden h-full">
        {currentClip ? <div ref={videoContainerRef} className="w-full h-full flex items-center justify-center relative" onMouseDown={handleMouseDown} onMouseMove={handleMouseMove} onMouseUp={handleMouseUp}>
            <video ref={videoRef} src={currentClip.url} className="relative z-0" style={videoStyle} onTimeUpdate={onTimeUpdate} onEnded={onEnded} />
            
            {/* Subtitle Overlay */}
            {getCurrentSubtitle() && videoContainerRef.current && (
              <SubtitleOverlay
                subtitle={getCurrentSubtitle()!}
                videoWidth={videoContainerRef.current.clientWidth}
                videoHeight={videoContainerRef.current.clientHeight}
              />
            )}
            
            {/* Video Overlay for guides and rulers */}
            {showOverlays && videoContainerRef.current && <VideoOverlay width={Math.min(videoContainerRef.current.clientWidth, window.innerWidth * 0.9)} height={Math.min(videoContainerRef.current.clientHeight, window.innerHeight * 0.7)} zoom={smoothZoom} showGuides={true} showRulers={smoothZoom > 100} />}

            {/* Quality Indicator */}
            <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-1">
              <span className="text-xs text-white uppercase tracking-wide">
                {videoQuality === 'auto' ? 'Auto' : videoQuality.toUpperCase()}
              </span>
            </div>

            {/* Zoom Level Indicator */}
            {Math.round(smoothZoom) !== 100 && <div className="absolute top-4 right-4 bg-purple-600/90 backdrop-blur-sm rounded-lg px-3 py-1">
                <span className="text-xs text-white">Zoom: {Math.round(smoothZoom)}%</span>
              </div>}

            {/* Overlay Controls Toggle */}
            <div className="absolute bottom-4 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-1">
              <button onClick={() => setShowOverlays(!showOverlays)} className="text-xs text-white hover:text-gray-300 transition-colors">
                Guides: {showOverlays ? 'ON' : 'OFF'}
              </button>
            </div>
          </div> : <div className="w-full h-full flex flex-col items-center justify-center border-2 border-dashed border-gray-600 m-4 rounded-lg cursor-pointer hover:border-blue-400 transition-colors" onDragOver={onDragOver} onDrop={onDrop} onClick={onUploadClick}>
            <Upload className="w-12 h-12 text-gray-400 mb-4" />
            <p className="text-lg text-gray-400 mb-2">Drop video files here or click to upload</p>
            <p className="text-sm text-gray-500">Supports MP4, MOV, AVI, WebM</p>
          </div>}
      </div>

      {/* Enhanced Video Controls */}
      {currentClip && <VideoControls isPlaying={isPlaying} currentTime={currentTime} duration={currentClip.duration} volume={volume} playbackRate={playbackRate} canGoBack={canGoBack} canGoForward={canGoForward} onPlay={onPlay} onPause={onPause} onSeek={onSeek} onVolumeChange={onVolumeChange} onPlaybackRateChange={handlePlaybackRateChange} onSkipBack={onSkipBack} onSkipForward={onSkipForward} onFullscreen={handleFullscreen} onFrameBack={() => handleFrameNavigation('back')} onFrameForward={() => handleFrameNavigation('forward')} />}
    </div>;
};
