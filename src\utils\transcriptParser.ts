interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

interface TranscriptSegment {
  text: string;
  startTime?: number;
  endTime?: number;
}

interface ParsedTranscript {
  segments: TranscriptSegment[];
  format: 'plain' | 'srt' | 'vtt' | 'json';
  totalDuration?: number;
}

export class TranscriptParser {
  /**
   * Parse transcript content from various formats
   */
  static parseTranscript(content: string, format?: string): ParsedTranscript {
    const trimmedContent = content.trim();
    
    // Auto-detect format if not provided
    if (!format) {
      format = this.detectFormat(trimmedContent);
    }

    switch (format.toLowerCase()) {
      case 'srt':
        return this.parseSRT(trimmedContent);
      case 'vtt':
      case 'webvtt':
        return this.parseVTT(trimmedContent);
      case 'json':
        return this.parseJSON(trimmedContent);
      case 'plain':
      default:
        return this.parsePlainText(trimmedContent);
    }
  }

  /**
   * Auto-detect transcript format
   */
  private static detectFormat(content: string): string {
    // Check for SRT format (numbered entries with timestamps)
    if (/^\d+\s*\n\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/m.test(content)) {
      return 'srt';
    }
    
    // Check for VTT format
    if (content.startsWith('WEBVTT') || /\d{2}:\d{2}:\d{2}\.\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}\.\d{3}/m.test(content)) {
      return 'vtt';
    }
    
    // Check for JSON format
    try {
      JSON.parse(content);
      return 'json';
    } catch {
      // Not JSON
    }
    
    return 'plain';
  }

  /**
   * Parse SRT format
   */
  private static parseSRT(content: string): ParsedTranscript {
    const segments: TranscriptSegment[] = [];
    const blocks = content.split(/\n\s*\n/).filter(block => block.trim());

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length < 3) continue;

      // Skip the sequence number (first line)
      const timeLine = lines[1];
      const textLines = lines.slice(2);

      const timeMatch = timeLine.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
      if (timeMatch) {
        const startTime = this.parseTimeToSeconds(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
        const endTime = this.parseTimeToSeconds(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
        
        segments.push({
          text: textLines.join(' ').trim(),
          startTime,
          endTime
        });
      }
    }

    return {
      segments,
      format: 'srt',
      totalDuration: segments.length > 0 ? Math.max(...segments.map(s => s.endTime || 0)) : undefined
    };
  }

  /**
   * Parse VTT format
   */
  private static parseVTT(content: string): ParsedTranscript {
    const segments: TranscriptSegment[] = [];
    const lines = content.split('\n');
    let i = 0;

    // Skip WEBVTT header and metadata
    while (i < lines.length && !lines[i].includes('-->')) {
      i++;
    }

    while (i < lines.length) {
      const line = lines[i].trim();
      
      if (line.includes('-->')) {
        const timeMatch = line.match(/(\d{2}):(\d{2}):(\d{2})\.(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2})\.(\d{3})/);
        if (timeMatch) {
          const startTime = this.parseTimeToSeconds(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
          const endTime = this.parseTimeToSeconds(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
          
          // Collect text lines until empty line or next timestamp
          const textLines: string[] = [];
          i++;
          while (i < lines.length && lines[i].trim() && !lines[i].includes('-->')) {
            textLines.push(lines[i].trim());
            i++;
          }
          
          if (textLines.length > 0) {
            segments.push({
              text: textLines.join(' ').trim(),
              startTime,
              endTime
            });
          }
        }
      }
      i++;
    }

    return {
      segments,
      format: 'vtt',
      totalDuration: segments.length > 0 ? Math.max(...segments.map(s => s.endTime || 0)) : undefined
    };
  }

  /**
   * Parse JSON format
   */
  private static parseJSON(content: string): ParsedTranscript {
    try {
      const data = JSON.parse(content);
      const segments: TranscriptSegment[] = [];

      if (Array.isArray(data)) {
        // Array of segments
        for (const item of data) {
          if (typeof item === 'string') {
            segments.push({ text: item });
          } else if (item.text) {
            segments.push({
              text: item.text,
              startTime: item.startTime || item.start,
              endTime: item.endTime || item.end
            });
          }
        }
      } else if (data.segments && Array.isArray(data.segments)) {
        // Object with segments array
        for (const segment of data.segments) {
          segments.push({
            text: segment.text,
            startTime: segment.startTime || segment.start,
            endTime: segment.endTime || segment.end
          });
        }
      }

      return {
        segments,
        format: 'json',
        totalDuration: data.totalDuration || (segments.length > 0 ? Math.max(...segments.map(s => s.endTime || 0)) : undefined)
      };
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }

  /**
   * Parse plain text format
   */
  private static parsePlainText(content: string): ParsedTranscript {
    // Split by paragraphs or sentences
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim());
    const segments: TranscriptSegment[] = [];

    if (paragraphs.length === 0) {
      // Split by sentences if no paragraphs
      const sentences = content.split(/[.!?]+/).filter(s => s.trim());
      segments.push(...sentences.map(text => ({ text: text.trim() })));
    } else {
      segments.push(...paragraphs.map(text => ({ text: text.replace(/\n/g, ' ').trim() })));
    }

    return {
      segments,
      format: 'plain'
    };
  }

  /**
   * Convert time components to seconds
   */
  private static parseTimeToSeconds(hours: string, minutes: string, seconds: string, milliseconds: string): number {
    return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds) + parseInt(milliseconds) / 1000;
  }

  /**
   * Convert transcript segments to subtitle objects
   */
  static convertToSubtitles(
    parsedTranscript: ParsedTranscript,
    options: {
      videoDuration?: number;
      defaultDuration?: number;
      maxCharsPerSubtitle?: number;
      fontSize?: number;
      fontColor?: string;
      backgroundColor?: string;
      position?: 'top' | 'center' | 'bottom';
      fontFamily?: string;
    } = {}
  ): Subtitle[] {
    const {
      videoDuration,
      defaultDuration = 3,
      maxCharsPerSubtitle = 80,
      fontSize = 24,
      fontColor = '#FFFFFF',
      backgroundColor = 'rgba(0, 0, 0, 0.7)',
      position = 'bottom',
      fontFamily = 'Arial, sans-serif'
    } = options;

    const subtitles: Subtitle[] = [];
    const segments = parsedTranscript.segments;

    if (segments.length === 0) return subtitles;

    // If segments already have timing, use them directly
    if (segments.every(s => s.startTime !== undefined && s.endTime !== undefined)) {
      return segments.map((segment, index) => ({
        id: `transcript_${Date.now()}_${index}`,
        text: segment.text,
        startTime: segment.startTime!,
        endTime: segment.endTime!,
        fontSize,
        fontColor,
        backgroundColor,
        position,
        fontFamily
      }));
    }

    // Auto-generate timing for segments without timing
    const totalDuration = videoDuration || parsedTranscript.totalDuration || segments.length * defaultDuration;
    
    // Split long segments if needed
    const processedSegments: string[] = [];
    for (const segment of segments) {
      if (segment.text.length <= maxCharsPerSubtitle) {
        processedSegments.push(segment.text);
      } else {
        // Split long text into smaller chunks
        const chunks = this.splitTextIntoChunks(segment.text, maxCharsPerSubtitle);
        processedSegments.push(...chunks);
      }
    }

    // Distribute timing evenly across processed segments
    const segmentDuration = totalDuration / processedSegments.length;

    processedSegments.forEach((text, index) => {
      const startTime = index * segmentDuration;
      const endTime = Math.min((index + 1) * segmentDuration, totalDuration);

      subtitles.push({
        id: `transcript_${Date.now()}_${index}`,
        text,
        startTime,
        endTime,
        fontSize,
        fontColor,
        backgroundColor,
        position,
        fontFamily
      });
    });

    return subtitles;
  }

  /**
   * Split text into chunks that fit within character limit
   */
  private static splitTextIntoChunks(text: string, maxChars: number): string[] {
    const chunks: string[] = [];
    const words = text.split(' ');
    let currentChunk = '';

    for (const word of words) {
      if ((currentChunk + ' ' + word).length <= maxChars) {
        currentChunk = currentChunk ? currentChunk + ' ' + word : word;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk);
          currentChunk = word;
        } else {
          // Word is longer than maxChars, split it
          chunks.push(word.substring(0, maxChars));
          currentChunk = word.substring(maxChars);
        }
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  }
}
