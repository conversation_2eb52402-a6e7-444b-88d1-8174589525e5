# Audio Export Improvements - Simplified Approach

This document outlines the simplified approach to fix audio and video glitches during export.

## Issues Identified

The original export system had several issues that could cause glitches:

1. **Excessive Video Seeking**: Too frequent `currentTime` changes disrupted playback
2. **Complex Audio Processing**: Over-engineered audio transitions caused instability
3. **High Frame Rate Capture**: Canvas capture at high frame rates caused performance issues
4. **Timing Precision**: Too precise timing thresholds caused unnecessary operations
5. **Audio Context Complexity**: Complex audio processing introduced more failure points

## Simplified Improvements Made

### 1. Reduced Video Seeking Frequency

**Before:**
```javascript
// Too frequent seeking causing glitches
if (Math.abs(video.currentTime - targetSourceTime) > 0.1) {
  video.currentTime = targetSourceTime;
}
```

**After:**
```javascript
// Much more conservative seeking to minimize disruption
const timeDiff = Math.abs(video.currentTime - targetSourceTime);
if (timeDiff > 0.2) { // Larger threshold to minimize seeking
  video.currentTime = targetSourceTime;
  // Give video time to stabilize after seeking
  await new Promise(resolve => setTimeout(resolve, 16));
}
```

### 2. Simplified Audio Control

**Before:**
```javascript
// Complex audio transitions with scheduled value changes
gainNode.gain.cancelScheduledValues(currentTime);
gainNode.gain.setValueAtTime(gainNode.gain.value, currentTime);
gainNode.gain.linearRampToValueAtTime(0.8, currentTime + transitionDuration);
```

**After:**
```javascript
// Simple, immediate audio control
this.videoElements.forEach((v, clipId) => {
  if ((v as any).gainNode) {
    (v as any).gainNode.gain.value = clipId === activeClip.id ? 1.0 : 0;
  }
});
```

### 3. Stable Canvas Capture

**Before:**
```javascript
// High frame rate capture causing performance issues
const videoStream = this.canvas.captureStream(options.framerate);
```

**After:**
```javascript
// Conservative frame rate for stability
const captureFrameRate = Math.min(options.framerate, 30); // Cap at 30fps
const videoStream = this.canvas.captureStream(captureFrameRate);
  video.currentTime = targetSourceTime;
  // Wait a bit for the seek to complete
  await new Promise(resolve => setTimeout(resolve, 10));
}
```

### 4. Simplified Audio Context

**Before:**
```javascript
// Complex audio processing with compressors and filters
const compressor = audioContext.createDynamicsCompressor();
const lowPassFilter = audioContext.createBiquadFilter();
// ... complex setup
```

**After:**
```javascript
// Simple, stable audio context
const audioContext = new AudioContext({ sampleRate: 44100 });
const masterGain = audioContext.createGain();
masterGain.gain.value = 1.0;
masterGain.connect(destination);
```

### 5. Improved MediaRecorder Settings

**Before:**
```javascript
this.mediaRecorder = new MediaRecorder(combinedStream, {
  mimeType,
  videoBitsPerSecond,
  audioBitsPerSecond: 128000,
});

this.mediaRecorder.start(100); // 100ms chunks
```

**After:**
```javascript
const recorderOptions: MediaRecorderOptions = {
  mimeType,
  videoBitsPerSecond,
  audioBitsPerSecond: 192000, // Higher audio bitrate
};

// Enhanced settings for WebM
if (mimeType.includes('webm')) {
  recorderOptions.audioBitsPerSecond = 256000;
}

this.mediaRecorder = new MediaRecorder(combinedStream, recorderOptions);
this.mediaRecorder.start(50); // Smaller chunks for smoother processing
```

### 6. Better Frame Timing

**Before:**
```javascript
setTimeout(renderNextFrame, frameInterval);
```

**After:**
```javascript
// Use requestAnimationFrame for smoother timing when possible
if (typeof requestAnimationFrame !== 'undefined') {
  requestAnimationFrame(() => setTimeout(renderNextFrame, Math.max(0, frameInterval - 16)));
} else {
  setTimeout(renderNextFrame, frameInterval);
}
```

### 7. Graceful Audio Cleanup

**Added:**
```javascript
private async fadeOutAllAudio() {
  if (!this.audioContext) return;

  const currentTime = this.audioContext.currentTime;
  const fadeDuration = 0.1; // 100ms fade out

  this.videoElements.forEach(video => {
    const gainNode = (video as any).gainNode;
    if (gainNode) {
      gainNode.gain.cancelScheduledValues(currentTime);
      gainNode.gain.setValueAtTime(gainNode.gain.value, currentTime);
      gainNode.gain.linearRampToValueAtTime(0, currentTime + fadeDuration);
    }
  });

  // Wait for fade to complete
  await new Promise(resolve => setTimeout(resolve, fadeDuration * 1000 + 10));
}
```

## Technical Benefits

1. **Reduced Seeking**: Less frequent video seeking minimizes audio/video disruption
2. **Simplified Audio**: Removed complex audio processing that could cause instability
3. **Stable Frame Rate**: Capped canvas capture rate prevents performance issues
4. **Conservative Timing**: Larger thresholds reduce unnecessary operations
5. **Immediate Audio Control**: Simple gain changes without scheduled transitions
6. **Better Stability**: Fewer moving parts means fewer potential failure points

## Expected Results

- **Eliminated Glitches**: Both audio and video should be much smoother
- **Better Performance**: Reduced CPU load during export
- **More Reliable Export**: Fewer complex operations mean fewer potential issues
- **Consistent Quality**: Stable frame rate and timing for predictable results
- **Faster Processing**: Simplified operations should speed up export

## Testing Recommendations

1. Test with various video formats (MP4, WebM, MOV)
2. Test with different audio qualities and bitrates
3. Test transitions between multiple clips
4. Test with long-duration exports
5. Test with different export quality settings
6. Compare before/after audio quality using audio analysis tools

The improvements should significantly reduce or eliminate the audio glitches that were occurring during video export while maintaining or improving overall export quality and performance.
